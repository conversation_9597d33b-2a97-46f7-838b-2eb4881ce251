"use client";

import React, { useState } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useCSRF } from '@/hooks/useCSRF';
import { SecurityValidator } from '@/lib/validation';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [showResendButton, setShowResendButton] = useState(false);
  const router = useRouter();
  const { getHeaders, isLoading: csrfLoading } = useCSRF();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setResendMessage('');
    setShowResendButton(false);
    setIsLoading(true);

    try {
      // Sanitize inputs before submission
      const sanitizedEmail = SecurityValidator.sanitizeInput(email, { maxLength: 254 });
      const sanitizedPassword = SecurityValidator.sanitizeInput(password, { maxLength: 128 });

      // Validate inputs for security threats
      const emailValidation = SecurityValidator.validateSecurity(sanitizedEmail);
      const passwordValidation = SecurityValidator.validateSecurity(sanitizedPassword);

      if (!emailValidation.isValid) {
        setError(`Invalid email: ${emailValidation.threats.join(', ')}`);
        setIsLoading(false);
        return;
      }

      if (!passwordValidation.isValid) {
        setError(`Invalid password: ${passwordValidation.threats.join(', ')}`);
        setIsLoading(false);
        return;
      }

      const result = await signIn('credentials', {
        email: sanitizedEmail,
        password: sanitizedPassword,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
        // Check if this is an email verification error
        if (result.error.toLowerCase().includes('verify your email')) {
          setShowResendButton(true);
        }
      } else if (result?.ok) {
        // Get the updated session to ensure user is logged in
        const session = await getSession();
        if (session) {
          router.push('/');
          router.refresh();
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!email) return;

    setIsResending(true);
    setResendMessage('');

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setResendMessage('Verification email sent! Please check your inbox.');
        setShowResendButton(false);
      } else {
        setResendMessage(data.error || 'Failed to send verification email.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setResendMessage('An unexpected error occurred.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50 dark:bg-gray-950">
      <Card className="w-full max-w-sm">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl">Sign In</CardTitle>
          <CardDescription>
            Enter your email and password to access your account
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="grid gap-4">
            {/* CSRF Token - Hidden field for additional protection */}
            <input
              type="hidden"
              name="_csrf"
              value={getHeaders()['x-csrf-token'] || ''}
              aria-hidden="true"
            />

            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => {
                  const sanitized = SecurityValidator.sanitizeInput(e.target.value, { maxLength: 254 });
                  setEmail(sanitized);
                }}
                required
                disabled={isLoading}
                autoComplete="email"
                name="email"
                aria-label="Email address"
                aria-describedby="email-description"
                aria-invalid={error ? 'true' : 'false'}
              />
              <div id="email-description" className="sr-only">
                Enter your email address to sign in to your account
              </div>
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/auth/forgot-password"
                  className="ml-auto inline-block text-sm underline min-h-[44px] min-w-[44px] flex items-center justify-center py-2 px-1"
                >
                  Forgot your password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => {
                  const sanitized = SecurityValidator.sanitizeInput(e.target.value, { maxLength: 128 });
                  setPassword(sanitized);
                }}
                required
                disabled={isLoading}
                autoComplete="current-password"
                name="password"
                aria-label="Password"
                aria-describedby="password-description"
                aria-invalid={error ? 'true' : 'false'}
              />
              <div id="password-description" className="sr-only">
                Enter your password to sign in to your account
              </div>
            </div>
            {error && (
              <div className="text-red-500 text-sm bg-red-50 dark:bg-red-900/20 p-2 rounded-md">
                {error}
              </div>
            )}
            {resendMessage && (
              <div className={`text-sm p-2 rounded-md ${
                resendMessage.includes('sent') || resendMessage.includes('successfully')
                  ? 'text-green-700 bg-green-50 dark:bg-green-900/20'
                  : 'text-red-500 bg-red-50 dark:bg-red-900/20'
              }`}>
                {resendMessage}
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button
              className="w-full"
              type="submit"
              disabled={isLoading || csrfLoading}
            >
              {isLoading ? 'Signing in...' : csrfLoading ? 'Loading...' : 'Sign in'}
            </Button>
            {showResendButton && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={handleResendVerification}
                disabled={isResending}
              >
                {isResending ? 'Sending...' : 'Resend verification email'}
              </Button>
            )}
            <div className="text-center text-sm text-gray-500">
              Don't have an account?{" "}
              <Link href="/signup" className="underline min-h-[44px] min-w-[44px] inline-flex items-center justify-center py-2 px-1">
                Sign up
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default LoginForm;
