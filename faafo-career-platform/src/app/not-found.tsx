import Link from 'next/link';
import { Home, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <div className="max-w-md w-full bg-card rounded-lg shadow-lg p-8 text-center border">
        <div className="mb-6">
          <h1 className="text-6xl font-bold text-primary mb-2">
            404
          </h1>
          <h2 className="text-xl font-semibold text-foreground mb-4">
            Page Not Found
          </h2>
          <p className="text-muted-foreground">
            Sorry, we couldn't find the page you're looking for. The page may have been moved, deleted, or you may have entered an incorrect URL.
          </p>
        </div>

        <div className="space-y-3">
          <Button asChild className="w-full">
            <Link href="/">
              <Home className="h-4 w-4 mr-2" />
              Return to Home
            </Link>
          </Button>

          <Button variant="outline" asChild className="w-full">
            <Link href="/assessment">
              <Search className="h-4 w-4 mr-2" />
              Take Assessment
            </Link>
          </Button>
        </div>

        <div className="mt-6 text-sm text-muted-foreground">
          <p>
            If you believe this is an error, please contact support.
          </p>
        </div>
      </div>
    </div>
  );
}
